// TemplateMo 558 Custom JavaScript
(function($) {
    'use strict';

    // Add template indicator
    $(document).ready(function() {
        // Add visual indicator that TemplateMo 558 is active
        $('body').append('<div class="test-template-indicator">TemplateMo 558 Active!</div>');
        
        // Add some fun effects
        $('.test-template-indicator').fadeIn(1000);
        
        // Pulse effect for service items
        $('.services .service-item').hover(
            function() {
                $(this).addClass('animated pulse');
            },
            function() {
                $(this).removeClass('animated pulse');
            }
        );
        
        // Add click effect to buttons
        $('.main-button a').click(function(e) {
            $(this).addClass('animated rubberBand');
            setTimeout(() => {
                $(this).removeClass('animated rubberBand');
            }, 1000);
        });
        
        // Console log to confirm template is loaded
        console.log('🎨 TemplateMo 558 JavaScript loaded successfully!');
        console.log('Template switching system is working correctly.');
        
        // Add some interactive elements
        $('.section-heading h2').click(function() {
            $(this).addClass('animated bounce');
            setTimeout(() => {
                $(this).removeClass('animated bounce');
            }, 1000);
        });
        
        // Show notification that template switched
        if (window.location.hash === '#template-switched') {
            showTemplateNotification();
        }
    });
    
    function showTemplateNotification() {
        const notification = $('<div class="template-switch-notification">')
            .html('🎉 Template successfully switched to TemplateMo 558!')
            .css({
                'position': 'fixed',
                'top': '50px',
                'right': '20px',
                'background': '#4CAF50',
                'color': 'white',
                'padding': '15px 20px',
                'border-radius': '5px',
                'z-index': '10000',
                'box-shadow': '0 4px 15px rgba(0,0,0,0.2)',
                'font-weight': 'bold'
            });
            
        $('body').append(notification);
        
        notification.fadeIn(500).delay(3000).fadeOut(500, function() {
            $(this).remove();
        });
    }

})(jQuery);
