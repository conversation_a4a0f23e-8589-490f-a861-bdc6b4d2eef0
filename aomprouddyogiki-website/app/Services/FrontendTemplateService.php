<?php

namespace App\Services;

use App\Models\CmsTemplateModel;
use App\Models\CmsTemplateAssetModel;
use App\Models\CmsTemplateSettingModel;

class FrontendTemplateService
{
    protected $templateModel;
    protected $assetModel;
    protected $settingModel;
    protected $activeTemplate;

    public function __construct()
    {
        try {
            $this->templateModel = new CmsTemplateModel();
            $this->assetModel = new CmsTemplateAssetModel();
            $this->settingModel = new CmsTemplateSettingModel();
        } catch (\Exception $e) {
            log_message('error', 'FrontendTemplateService constructor error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get the currently active template
     */
    public function getActiveTemplate()
    {
        if ($this->activeTemplate === null) {
            $this->activeTemplate = $this->templateModel->getActiveTemplate();

            // Fallback to Space Dynamic if no active template found
            if (!$this->activeTemplate) {
                $this->activeTemplate = $this->templateModel->getBySlug('space-dynamic');

                // If Space Dynamic exists but isn't active, make it active
                if ($this->activeTemplate && !$this->activeTemplate['is_default']) {
                    $this->templateModel->setActiveTemplate($this->activeTemplate['id']);
                    $this->activeTemplate['is_default'] = true;
                    $this->activeTemplate['status'] = 'active';
                }
            }
        }

        return $this->activeTemplate;
    }

    /**
     * Get CSS assets for the active template
     */
    public function getActiveCssAssets()
    {
        try {
            $template = $this->getActiveTemplate();

            if (!$template) {
                return $this->getFallbackCssAssets();
            }

            $assets = $this->assetModel->getCssAssets($template['id']);

            // If no assets found, use fallback
            if (empty($assets)) {
                return $this->getFallbackCssAssets();
            }

            // Sort by load_order
            usort($assets, function($a, $b) {
                return ($a['load_order'] ?? 999) - ($b['load_order'] ?? 999);
            });

            return $assets;
        } catch (\Exception $e) {
            log_message('error', 'getActiveCssAssets error: ' . $e->getMessage());
            return $this->getFallbackCssAssets();
        }
    }

    /**
     * Get fallback CSS assets
     */
    protected function getFallbackCssAssets()
    {
        return [
            ['file_path' => 'css/fontawesome.css', 'load_order' => 1, 'is_critical' => 1],
            ['file_path' => 'css/templatemo-space-dynamic.css', 'load_order' => 2, 'is_critical' => 1],
            ['file_path' => 'css/animated.css', 'load_order' => 3, 'is_critical' => 0],
            ['file_path' => 'css/owl.css', 'load_order' => 4, 'is_critical' => 0],
        ];
    }

    /**
     * Get JavaScript assets for the active template
     */
    public function getActiveJsAssets()
    {
        try {
            $template = $this->getActiveTemplate();

            if (!$template) {
                return $this->getFallbackJsAssets();
            }

            $assets = $this->assetModel->getJsAssets($template['id']);

            // If no assets found, use fallback
            if (empty($assets)) {
                return $this->getFallbackJsAssets();
            }

            // Sort by load_order
            usort($assets, function($a, $b) {
                return ($a['load_order'] ?? 999) - ($b['load_order'] ?? 999);
            });

            return $assets;
        } catch (\Exception $e) {
            log_message('error', 'getActiveJsAssets error: ' . $e->getMessage());
            return $this->getFallbackJsAssets();
        }
    }

    /**
     * Get fallback JavaScript assets
     */
    protected function getFallbackJsAssets()
    {
        return [
            ['file_path' => 'js/owl-carousel.js', 'load_order' => 1, 'is_critical' => 0],
            ['file_path' => 'js/animation.js', 'load_order' => 2, 'is_critical' => 0],
            ['file_path' => 'js/imagesloaded.js', 'load_order' => 3, 'is_critical' => 0],
            ['file_path' => 'js/templatemo-custom.js', 'load_order' => 4, 'is_critical' => 1],
        ];
    }

    /**
     * Get critical CSS assets (for head section)
     */
    public function getCriticalCssAssets()
    {
        $template = $this->getActiveTemplate();
        
        if (!$template) {
            return [
                ['file_path' => 'css/fontawesome.css', 'load_order' => 1],
                ['file_path' => 'css/templatemo-space-dynamic.css', 'load_order' => 2],
            ];
        }

        $assets = $this->assetModel->getCssAssets($template['id']);
        
        // Filter for critical assets
        $criticalAssets = array_filter($assets, function($asset) {
            return $asset['is_critical'] == 1;
        });

        // If no critical assets, return all CSS
        if (empty($criticalAssets)) {
            return $assets;
        }

        return $criticalAssets;
    }

    /**
     * Get non-critical CSS assets (for async loading)
     */
    public function getNonCriticalCssAssets()
    {
        $template = $this->getActiveTemplate();
        
        if (!$template) {
            return [
                ['file_path' => 'css/animated.css', 'load_order' => 3],
                ['file_path' => 'css/owl.css', 'load_order' => 4],
            ];
        }

        $assets = $this->assetModel->getCssAssets($template['id']);
        
        // Filter for non-critical assets
        return array_filter($assets, function($asset) {
            return $asset['is_critical'] != 1;
        });
    }

    /**
     * Get template configuration
     */
    public function getActiveTemplateConfig()
    {
        $template = $this->getActiveTemplate();
        
        if (!$template) {
            return [
                'name' => 'Space Dynamic',
                'slug' => 'space-dynamic',
                'folder_path' => 'themes/space-dynamic',
                'config_data' => []
            ];
        }

        return $template;
    }

    /**
     * Get template theme directory path
     */
    public function getActiveThemePath()
    {
        $template = $this->getActiveTemplate();
        
        if (!$template) {
            return 'themes/space-dynamic';
        }

        return $template['folder_path'];
    }

    /**
     * Check if a template asset exists
     */
    public function assetExists($filePath)
    {
        $fullPath = FCPATH . $filePath;
        return file_exists($fullPath);
    }

    /**
     * Get asset URL with fallback
     */
    public function getAssetUrl($filePath, $fallbackPath = null)
    {
        if ($this->assetExists($filePath)) {
            return base_url($filePath);
        }

        if ($fallbackPath && $this->assetExists($fallbackPath)) {
            return base_url($fallbackPath);
        }

        // Return the original path even if it doesn't exist
        // This will help identify missing assets
        return base_url($filePath);
    }

    /**
     * Get template information for debugging
     */
    public function getTemplateDebugInfo()
    {
        $template = $this->getActiveTemplate();
        
        return [
            'active_template' => $template,
            'css_assets' => $this->getActiveCssAssets(),
            'js_assets' => $this->getActiveJsAssets(),
            'theme_path' => $this->getActiveThemePath(),
            'template_count' => $this->templateModel->countAll(),
        ];
    }

    /**
     * Refresh template cache
     */
    public function refreshTemplateCache()
    {
        $this->activeTemplate = null;
        return $this->getActiveTemplate();
    }

    /**
     * Get template assets as HTML tags
     */
    public function getCssHtml()
    {
        try {
            $assets = $this->getActiveCssAssets();
            $html = '';

            foreach ($assets as $asset) {
                $filePath = $asset['file_path'] ?? '';
                if (!empty($filePath)) {
                    $url = $this->getAssetUrl($filePath);
                    $html .= '<link rel="stylesheet" href="' . esc($url) . '">' . "\n    ";
                }
            }

            return trim($html);
        } catch (\Exception $e) {
            log_message('error', 'getCssHtml error: ' . $e->getMessage());
            // Return hardcoded fallback
            return '<link rel="stylesheet" href="' . base_url('css/fontawesome.css') . '">' . "\n    " .
                   '<link rel="stylesheet" href="' . base_url('css/templatemo-space-dynamic.css') . '">' . "\n    " .
                   '<link rel="stylesheet" href="' . base_url('css/animated.css') . '">' . "\n    " .
                   '<link rel="stylesheet" href="' . base_url('css/owl.css') . '">';
        }
    }

    /**
     * Get JavaScript assets as HTML tags
     */
    public function getJsHtml()
    {
        try {
            $assets = $this->getActiveJsAssets();
            $html = '';

            foreach ($assets as $asset) {
                $filePath = $asset['file_path'] ?? '';
                if (!empty($filePath)) {
                    $url = $this->getAssetUrl($filePath);
                    $html .= '<script src="' . esc($url) . '"></script>' . "\n    ";
                }
            }

            return trim($html);
        } catch (\Exception $e) {
            log_message('error', 'getJsHtml error: ' . $e->getMessage());
            // Return hardcoded fallback
            return '<script src="' . base_url('js/owl-carousel.js') . '"></script>' . "\n    " .
                   '<script src="' . base_url('js/animation.js') . '"></script>' . "\n    " .
                   '<script src="' . base_url('js/imagesloaded.js') . '"></script>' . "\n    " .
                   '<script src="' . base_url('js/templatemo-custom.js') . '"></script>';
        }
    }
}
