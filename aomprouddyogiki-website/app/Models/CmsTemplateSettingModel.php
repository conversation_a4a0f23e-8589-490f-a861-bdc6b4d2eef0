<?php

namespace App\Models;

use CodeIgniter\Model;

class CmsTemplateSettingModel extends Model
{
    protected $table = 'cms_template_settings';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'setting_key',
        'setting_value',
        'setting_type',
        'description',
        'updated_at'
    ];

    // Dates
    protected $useTimestamps = false; // Only updated_at
    protected $dateFormat = 'datetime';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'setting_key' => 'required|min_length[3]|max_length[255]|is_unique[cms_template_settings.setting_key,id,{id}]',
        'setting_type' => 'required|in_list[string,integer,boolean,json]',
        'description' => 'permit_empty|max_length[500]'
    ];

    protected $validationMessages = [
        'setting_key' => [
            'required' => 'Setting key is required',
            'is_unique' => 'Setting key must be unique'
        ],
        'setting_type' => [
            'required' => 'Setting type is required',
            'in_list' => 'Invalid setting type'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['setUpdatedAt', 'validateSettingValue'];
    protected $beforeUpdate = ['setUpdatedAt', 'validateSettingValue'];

    /**
     * Set updated_at timestamp
     */
    protected function setUpdatedAt(array $data)
    {
        $data['data']['updated_at'] = date('Y-m-d H:i:s');
        return $data;
    }

    /**
     * Validate setting value based on type
     */
    protected function validateSettingValue(array $data)
    {
        if (!isset($data['data']['setting_value']) || !isset($data['data']['setting_type'])) {
            return $data;
        }

        $value = $data['data']['setting_value'];
        $type = $data['data']['setting_type'];

        switch ($type) {
            case 'integer':
                if (!is_numeric($value)) {
                    throw new \InvalidArgumentException('Setting value must be numeric for integer type');
                }
                $data['data']['setting_value'] = (string)(int)$value;
                break;
            
            case 'boolean':
                if (!in_array($value, ['0', '1', 'true', 'false', true, false, 0, 1], true)) {
                    throw new \InvalidArgumentException('Setting value must be boolean for boolean type');
                }
                $data['data']['setting_value'] = in_array($value, ['1', 'true', true, 1], true) ? '1' : '0';
                break;
            
            case 'json':
                if (is_array($value)) {
                    $data['data']['setting_value'] = json_encode($value);
                } elseif (is_string($value)) {
                    $decoded = json_decode($value, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        throw new \InvalidArgumentException('Setting value must be valid JSON for json type');
                    }
                    $data['data']['setting_value'] = $value;
                }
                break;
            
            case 'string':
            default:
                $data['data']['setting_value'] = (string)$value;
                break;
        }

        return $data;
    }

    /**
     * Parse setting value based on type after finding
     */
    protected function parseSettingValue(array $data)
    {
        if (isset($data['data'])) {
            // Single record
            $data['data'] = $this->parseSingleSetting($data['data']);
        } else {
            // Multiple records
            if (is_array($data['data'])) {
                foreach ($data['data'] as &$record) {
                    $record = $this->parseSingleSetting($record);
                }
            }
        }
        return $data;
    }

    /**
     * Parse single setting value
     */
    private function parseSingleSetting($setting)
    {
        if (!isset($setting['setting_value']) || !isset($setting['setting_type'])) {
            return $setting;
        }

        $value = $setting['setting_value'];
        $type = $setting['setting_type'];

        switch ($type) {
            case 'integer':
                $setting['parsed_value'] = (int)$value;
                break;
            
            case 'boolean':
                $setting['parsed_value'] = in_array($value, ['1', 'true'], true);
                break;
            
            case 'json':
                $setting['parsed_value'] = json_decode($value, true);
                break;
            
            case 'string':
            default:
                $setting['parsed_value'] = $value;
                break;
        }

        return $setting;
    }

    /**
     * Get setting by key
     */
    public function getSetting($key, $default = null)
    {
        $setting = $this->where('setting_key', $key)->first();

        if (!$setting) {
            return $default;
        }

        $setting = $this->parseSingleSetting($setting);
        return $setting['parsed_value'] ?? $setting['setting_value'];
    }

    /**
     * Set setting value
     */
    public function setSetting($key, $value, $type = 'string', $description = null)
    {
        $existing = $this->where('setting_key', $key)->first();

        $data = [
            'setting_key' => $key,
            'setting_value' => $value,
            'setting_type' => $type,
            'description' => $description,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($existing) {
            return $this->update($existing['id'], $data);
        } else {
            return $this->insert($data);
        }
    }

    /**
     * Get active template slug
     */
    public function getActiveTemplate()
    {
        return $this->getSetting('active_template', 'space-dynamic');
    }

    /**
     * Set active template
     */
    public function setActiveTemplate($templateSlug)
    {
        return $this->setSetting('active_template', $templateSlug, 'string', 'Currently active template slug');
    }

    /**
     * Get all settings as key-value pairs
     */
    public function getAllSettings()
    {
        $settings = $this->findAll();
        $result = [];

        foreach ($settings as $setting) {
            $setting = $this->parseSingleSetting($setting);
            $result[$setting['setting_key']] = $setting['parsed_value'] ?? $setting['setting_value'];
        }

        return $result;
    }

    /**
     * Get settings by prefix
     */
    public function getSettingsByPrefix($prefix)
    {
        $settings = $this->like('setting_key', $prefix, 'after')->findAll();
        $result = [];

        foreach ($settings as $setting) {
            $setting = $this->parseSingleSetting($setting);
            $result[$setting['setting_key']] = $setting['parsed_value'] ?? $setting['setting_value'];
        }

        return $result;
    }

    /**
     * Update multiple settings
     */
    public function updateSettings($settingsArray)
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            foreach ($settingsArray as $key => $data) {
                if (is_array($data)) {
                    $value = $data['value'] ?? '';
                    $type = $data['type'] ?? 'string';
                    $description = $data['description'] ?? null;
                } else {
                    $value = $data;
                    $type = 'string';
                    $description = null;
                }

                $this->setSetting($key, $value, $type, $description);
            }

            $db->transComplete();
            return $db->transStatus();
        } catch (\Exception $e) {
            $db->transRollback();
            return false;
        }
    }

    /**
     * Delete setting
     */
    public function deleteSetting($key)
    {
        return $this->where('setting_key', $key)->delete();
    }

    /**
     * Check if backup is enabled
     */
    public function isBackupEnabled()
    {
        return $this->getSetting('template_backup_enabled', true);
    }

    /**
     * Check if auto parse sections is enabled
     */
    public function isAutoParseSectionsEnabled()
    {
        return $this->getSetting('auto_parse_sections', true);
    }

    /**
     * Get template engine configuration
     */
    public function getTemplateEngineConfig()
    {
        return [
            'active_template' => $this->getActiveTemplate(),
            'backup_enabled' => $this->isBackupEnabled(),
            'auto_parse_sections' => $this->isAutoParseSectionsEnabled(),
            'themes_directory' => $this->getSetting('themes_directory', 'themes'),
            'max_template_size' => $this->getSetting('max_template_size', 50), // MB
            'allowed_extensions' => $this->getSetting('allowed_extensions', ['zip', 'tar.gz'], 'json'),
        ];
    }

    /**
     * Reset to default settings
     */
    public function resetToDefaults()
    {
        $defaults = [
            'active_template' => ['value' => 'space-dynamic', 'type' => 'string', 'description' => 'Currently active template slug'],
            'template_backup_enabled' => ['value' => '1', 'type' => 'boolean', 'description' => 'Enable automatic template backup before switching'],
            'auto_parse_sections' => ['value' => '1', 'type' => 'boolean', 'description' => 'Automatically parse template sections during installation'],
            'themes_directory' => ['value' => 'themes', 'type' => 'string', 'description' => 'Directory for storing template files'],
            'max_template_size' => ['value' => '50', 'type' => 'integer', 'description' => 'Maximum template file size in MB'],
        ];

        return $this->updateSettings($defaults);
    }

    /**
     * Export settings
     */
    public function exportSettings()
    {
        $settings = $this->findAll();
        $export = [];

        foreach ($settings as $setting) {
            $export[$setting['setting_key']] = [
                'value' => $setting['setting_value'],
                'type' => $setting['setting_type'],
                'description' => $setting['description']
            ];
        }

        return $export;
    }

    /**
     * Import settings
     */
    public function importSettings($settingsData)
    {
        return $this->updateSettings($settingsData);
    }
}
