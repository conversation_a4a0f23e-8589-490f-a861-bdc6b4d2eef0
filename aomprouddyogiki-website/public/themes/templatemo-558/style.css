/* TemplateMo 558 Test Template */
body {
    background-color: #f0f8ff;
    font-family: 'Arial', sans-serif;
    color: #333;
}

.test-template-indicator {
    position: fixed;
    top: 10px;
    right: 10px;
    background: #ff6b6b;
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    z-index: 9999;
    font-weight: bold;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.test-template-indicator::before {
    content: "🎨 ";
}

/* Override some Space Dynamic styles for testing */
.main-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.main-banner h2 {
    color: #fff !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.main-banner h2::after {
    content: " (TemplateMo 558 Active!)";
    font-size: 0.6em;
    color: #ffeb3b;
}

.services .service-item {
    border: 3px solid #ff6b6b;
    border-radius: 15px;
    background: linear-gradient(45deg, #fff, #f8f9fa);
}

.services .service-item:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
}

/* Add some fun animations */
@keyframes templatemo558pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.section-heading h2 {
    animation: templatemo558pulse 2s infinite;
    color: #667eea !important;
}

/* Footer styling */
footer {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

footer p {
    color: #fff !important;
}

/* Navigation styling */
.header-area .main-nav .nav li a {
    color: #667eea !important;
    font-weight: 600;
}

.header-area .main-nav .nav li a:hover {
    color: #ff6b6b !important;
}
