<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\CmsTemplateModel;
use App\Models\CmsTemplateSectionModel;
use App\Models\CmsTemplateAssetModel;
use App\Models\CmsTemplateSettingModel;
use App\Services\TemplateEngineService;
use App\Services\TemplateParserService;

class CmsTemplates extends BaseController
{
    protected $templateModel;
    protected $sectionModel;
    protected $assetModel;
    protected $settingModel;
    protected $templateEngineService;
    protected $templateParserService;

    public function __construct()
    {
        $this->templateModel = new CmsTemplateModel();
        $this->sectionModel = new CmsTemplateSectionModel();
        $this->assetModel = new CmsTemplateAssetModel();
        $this->settingModel = new CmsTemplateSettingModel();
        $this->templateEngineService = new TemplateEngineService();
        $this->templateParserService = new TemplateParserService();
    }

    /**
     * Template gallery - main templates page
     */
    public function index()
    {
        // Check admin authentication
        if (!session()->get('admin_logged_in')) {
            return redirect()->to('/admin/login');
        }

        $data = [
            'title' => 'Template Gallery - Template Engine',
            'templates' => $this->templateModel->getAvailableTemplates(),
            'activeTemplate' => $this->templateEngineService->getActiveTemplate(),
            'stats' => $this->templateEngineService->getTemplateEngineStats()
        ];

        return view('admin/templates/index', $data);
    }

    /**
     * Template details view
     */
    public function view($templateId)
    {
        if (!session()->get('admin_logged_in')) {
            return redirect()->to('/admin/login');
        }

        $template = $this->templateModel->getTemplateWithDetails($templateId);
        
        if (!$template) {
            session()->setFlashdata('error', 'Template not found');
            return redirect()->to('/admin/templates');
        }

        $data = [
            'title' => $template['name'] . ' - Template Details',
            'template' => $template,
            'validation' => $this->sectionModel->validateTemplateStructure($templateId)
        ];

        return view('admin/templates/view', $data);
    }

    /**
     * Switch active template
     */
    public function switch()
    {
        if (!session()->get('admin_logged_in')) {
            return redirect()->to('/admin/login');
        }

        if ($this->request->getMethod() === 'POST') {
            $templateId = $this->request->getPost('template_id');
            $createBackup = $this->request->getPost('create_backup') === '1';

            // Debug logging
            log_message('info', 'Template switch requested - ID: ' . $templateId . ', Backup: ' . ($createBackup ? 'yes' : 'no'));

            try {
                $template = $this->templateModel->find($templateId);

                if (!$template) {
                    throw new \Exception('Template not found');
                }

                log_message('info', 'Switching to template: ' . $template['name'] . ' (' . $template['slug'] . ')');

                $success = $this->templateEngineService->switchTemplate($template['slug'], $createBackup);

                if ($success) {
                    log_message('info', 'Template switch successful');
                    session()->setFlashdata('success', "Successfully switched to template: {$template['name']}");
                } else {
                    log_message('error', 'Template switch failed - service returned false');
                    session()->setFlashdata('error', 'Failed to switch template');
                }
            } catch (\Exception $e) {
                log_message('error', 'Template switch exception: ' . $e->getMessage());
                session()->setFlashdata('error', 'Error switching template: ' . $e->getMessage());
            }
        }

        return redirect()->to('/admin/templates');
    }

    /**
     * Install new template
     */
    public function install()
    {
        if (!session()->get('admin_logged_in')) {
            return redirect()->to('/admin/login');
        }

        if ($this->request->getMethod() === 'POST') {
            $installType = $this->request->getPost('install_type');

            try {
                if ($installType === 'templatemo') {
                    $templatemoUrl = $this->request->getPost('templatemo_url');
                    $this->installFromTemplateM($templatemoUrl);
                } elseif ($installType === 'upload') {
                    $this->installFromUpload();
                } else {
                    throw new \Exception('Invalid installation type');
                }

                session()->setFlashdata('success', 'Template installation started. Check the templates list for progress.');
            } catch (\Exception $e) {
                session()->setFlashdata('error', 'Installation failed: ' . $e->getMessage());
            }

            return redirect()->to('/admin/templates');
        }

        // Show installation form
        $data = [
            'title' => 'Install New Template',
            'popularTemplates' => $this->getPopularTemplates()
        ];

        return view('admin/templates/install', $data);
    }

    /**
     * Install template from TemplateMo
     */
    protected function installFromTemplateM($templatemoUrl)
    {
        // Extract template ID for basic installation
        preg_match('/tm-(\d+)/', $templatemoUrl, $matches);
        $templatemoId = $matches[1] ?? null;

        if (!$templatemoId) {
            throw new \Exception('Invalid TemplateMo URL - could not extract template ID');
        }

        // Check if template already exists
        $existing = $this->templateModel->getBySlug("templatemo-{$templatemoId}");
        if ($existing) {
            throw new \Exception('Template already exists: TemplateMo ' . $templatemoId);
        }

        // For now, just create a basic template record without downloading
        // This avoids download issues while testing the system
        $templateData = [
            'name' => "TemplateMo {$templatemoId}",
            'slug' => "templatemo-{$templatemoId}",
            'description' => "Template from TemplateMo.com - {$templatemoUrl}",
            'templatemo_id' => "tm-{$templatemoId}",
            'source_url' => $templatemoUrl,
            'folder_path' => "themes/templatemo-{$templatemoId}",
            'status' => 'inactive',
            'author' => 'TemplateMo',
            'version' => '1.0.0',
            'config_data' => json_encode([
                'source' => 'templatemo',
                'manual_install' => true,
                'requires_download' => true
            ])
        ];

        $templateId = $this->templateModel->insert($templateData);

        if (!$templateId) {
            throw new \Exception('Failed to create template record');
        }

        return $templateId;
    }

    /**
     * Install template from file upload
     */
    protected function installFromUpload()
    {
        $uploadedFile = $this->request->getFile('template_file');
        
        if (!$uploadedFile->isValid()) {
            throw new \Exception('Invalid file upload');
        }

        $allowedTypes = ['application/zip', 'application/x-zip-compressed'];
        if (!in_array($uploadedFile->getMimeType(), $allowedTypes)) {
            throw new \Exception('Only ZIP files are allowed');
        }

        // Generate template info from filename
        $fileName = pathinfo($uploadedFile->getName(), PATHINFO_FILENAME);
        $templateSlug = url_title($fileName, '-', true);

        // Check if template already exists
        $existing = $this->templateModel->getBySlug($templateSlug);
        if ($existing) {
            throw new \Exception('Template already exists: ' . $fileName);
        }

        $templateData = [
            'name' => ucwords(str_replace(['-', '_'], ' ', $fileName)),
            'slug' => $templateSlug,
            'description' => 'Uploaded template: ' . $fileName,
            'folder_path' => 'themes/' . $templateSlug,
            'status' => 'installing'
        ];

        $templateId = $this->templateModel->installTemplate($templateData);

        if (!$templateId) {
            throw new \Exception('Failed to create template record');
        }

        // Move and extract uploaded file
        $themesPath = FCPATH . 'themes/' . $templateSlug . '/';
        
        if (!is_dir($themesPath)) {
            mkdir($themesPath, 0755, true);
        }

        $uploadedFile->move($themesPath, 'template.zip');
        
        // Extract ZIP file
        $zip = new \ZipArchive();
        if ($zip->open($themesPath . 'template.zip') === TRUE) {
            $zip->extractTo($themesPath);
            $zip->close();
            unlink($themesPath . 'template.zip');
        } else {
            throw new \Exception('Failed to extract ZIP file');
        }

        // Parse template structure
        $this->templateEngineService->parseTemplateStructure($templateId, $themesPath);

        // Update status to inactive (ready for activation)
        $this->templateModel->updateStatus($templateId, 'inactive');

        return $templateId;
    }

    /**
     * Delete template
     */
    public function delete($templateId)
    {
        if (!session()->get('admin_logged_in')) {
            return redirect()->to('/admin/login');
        }

        $template = $this->templateModel->find($templateId);
        
        if (!$template) {
            session()->setFlashdata('error', 'Template not found');
            return redirect()->to('/admin/templates');
        }

        if ($template['is_default']) {
            session()->setFlashdata('error', 'Cannot delete the active template');
            return redirect()->to('/admin/templates');
        }

        try {
            // Delete template files
            $templatePath = FCPATH . $template['folder_path'];
            if (is_dir($templatePath)) {
                $this->deleteDirectory($templatePath);
            }

            // Delete from database
            $success = $this->templateModel->deleteTemplate($templateId);

            if ($success) {
                session()->setFlashdata('success', 'Template deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete template');
            }
        } catch (\Exception $e) {
            session()->setFlashdata('error', 'Error deleting template: ' . $e->getMessage());
        }

        return redirect()->to('/admin/templates');
    }

    /**
     * Template sections management
     */
    public function sections($templateId)
    {
        if (!session()->get('admin_logged_in')) {
            return redirect()->to('/admin/login');
        }

        $template = $this->templateModel->find($templateId);
        
        if (!$template) {
            session()->setFlashdata('error', 'Template not found');
            return redirect()->to('/admin/templates');
        }

        $data = [
            'title' => $template['name'] . ' - Sections',
            'template' => $template,
            'sections' => $this->sectionModel->getByTemplateId($templateId),
            'sectionsGrouped' => $this->sectionModel->getSectionsGroupedByType($templateId),
            'stats' => $this->sectionModel->getSectionStats($templateId)
        ];

        return view('admin/templates/sections', $data);
    }

    /**
     * Template assets management
     */
    public function assets($templateId)
    {
        if (!session()->get('admin_logged_in')) {
            return redirect()->to('/admin/login');
        }

        $template = $this->templateModel->find($templateId);
        
        if (!$template) {
            session()->setFlashdata('error', 'Template not found');
            return redirect()->to('/admin/templates');
        }

        $data = [
            'title' => $template['name'] . ' - Assets',
            'template' => $template,
            'assets' => $this->assetModel->getByTemplateId($templateId),
            'assetsGrouped' => $this->assetModel->getAssetsGroupedByType($templateId),
            'stats' => $this->assetModel->getAssetStats($templateId)
        ];

        return view('admin/templates/assets', $data);
    }

    /**
     * Template settings
     */
    public function settings()
    {
        if (!session()->get('admin_logged_in')) {
            return redirect()->to('/admin/login');
        }

        if ($this->request->getMethod() === 'POST') {
            $settings = $this->request->getPost();
            
            try {
                $success = $this->settingModel->updateSettings($settings);
                
                if ($success) {
                    session()->setFlashdata('success', 'Settings updated successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to update settings');
                }
            } catch (\Exception $e) {
                session()->setFlashdata('error', 'Error updating settings: ' . $e->getMessage());
            }

            return redirect()->to('/admin/templates/settings');
        }

        $data = [
            'title' => 'Template Engine Settings',
            'settings' => $this->settingModel->getAllSettings(),
            'config' => $this->settingModel->getTemplateEngineConfig()
        ];

        return view('admin/templates/settings', $data);
    }

    /**
     * Get popular templates for installation
     */
    protected function getPopularTemplates()
    {
        try {
            return $this->templateParserService->getPopularTemplates();
        } catch (\Exception $e) {
            log_message('error', 'Failed to fetch popular templates: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Recursively delete directory
     */
    protected function deleteDirectory($dir)
    {
        if (!is_dir($dir)) {
            return false;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;
            is_dir($path) ? $this->deleteDirectory($path) : unlink($path);
        }

        return rmdir($dir);
    }

    /**
     * AJAX endpoint for template search
     */
    public function search()
    {
        if (!session()->get('admin_logged_in')) {
            return $this->response->setJSON(['error' => 'Unauthorized']);
        }

        $query = $this->request->getGet('q');

        if (empty($query)) {
            return $this->response->setJSON(['templates' => []]);
        }

        try {
            $templates = $this->templateParserService->searchTemplateM($query, 10);
            return $this->response->setJSON(['templates' => $templates]);
        } catch (\Exception $e) {
            return $this->response->setJSON(['error' => $e->getMessage()]);
        }
    }

    /**
     * Debug endpoint to check template status
     */
    public function debug()
    {
        if (!session()->get('admin_logged_in')) {
            return $this->response->setJSON(['error' => 'Unauthorized']);
        }

        $frontendService = new \App\Services\FrontendTemplateService();

        $debugInfo = [
            'database_active_template' => $this->templateModel->getActiveTemplate(),
            'frontend_active_template' => $frontendService->getActiveTemplate(),
            'all_templates' => $this->templateModel->findAll(),
            'template_settings' => $this->settingModel->getAllSettings(),
            'template_debug' => $frontendService->getTemplateDebugInfo()
        ];

        return $this->response->setJSON($debugInfo);
    }
}
