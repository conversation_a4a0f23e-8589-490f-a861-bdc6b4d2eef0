<?php

namespace App\Services;

use App\Models\CmsTemplateModel;
use App\Models\CmsTemplateSectionModel;
use App\Models\CmsTemplateAssetModel;
use App\Models\CmsTemplateSettingModel;

class TemplateEngineService
{
    protected $templateModel;
    protected $sectionModel;
    protected $assetModel;
    protected $settingModel;
    protected $themesPath;

    public function __construct()
    {
        $this->templateModel = new CmsTemplateModel();
        $this->sectionModel = new CmsTemplateSectionModel();
        $this->assetModel = new CmsTemplateAssetModel();
        $this->settingModel = new CmsTemplateSettingModel();
        $this->themesPath = FCPATH . 'themes/';
        
        // Ensure themes directory exists
        if (!is_dir($this->themesPath)) {
            mkdir($this->themesPath, 0755, true);
        }
    }

    /**
     * Get active template with all details
     */
    public function getActiveTemplate()
    {
        $activeTemplate = $this->templateModel->getActiveTemplate();
        
        if (!$activeTemplate) {
            return null;
        }

        return $this->templateModel->getTemplateWithDetails($activeTemplate['id']);
    }

    /**
     * Switch to a different template
     */
    public function switchTemplate($templateSlug, $createBackup = true)
    {
        $newTemplate = $this->templateModel->getBySlug($templateSlug);
        
        if (!$newTemplate) {
            throw new \Exception("Template '{$templateSlug}' not found");
        }

        if ($newTemplate['status'] !== 'active') {
            throw new \Exception("Template '{$templateSlug}' is not active");
        }

        $currentTemplate = $this->getActiveTemplate();

        try {
            // Create backup if enabled and requested
            if ($createBackup && $currentTemplate && $this->settingModel->isBackupEnabled()) {
                $this->createTemplateBackup($currentTemplate['slug']);
            }

            // Switch templates
            $success = $this->templateModel->setActiveTemplate($newTemplate['id']);
            
            if ($success) {
                $this->settingModel->setActiveTemplate($templateSlug);
                return true;
            }

            return false;
        } catch (\Exception $e) {
            log_message('error', 'Template switch failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Install template from templatemo.com
     */
    public function installTemplateFromTemplateM($templatemoId, $downloadUrl = null)
    {
        try {
            // Set status to installing
            $templateData = [
                'name' => "TemplateMo {$templatemoId}",
                'slug' => "templatemo-{$templatemoId}",
                'description' => "Template downloaded from TemplateMo.com",
                'templatemo_id' => $templatemoId,
                'folder_path' => "themes/templatemo-{$templatemoId}",
                'status' => 'installing'
            ];

            $templateId = $this->templateModel->installTemplate($templateData);
            
            if (!$templateId) {
                throw new \Exception('Failed to create template record');
            }

            // Download and extract template
            if ($downloadUrl) {
                try {
                    $extractPath = $this->downloadAndExtractTemplate($downloadUrl, "templatemo-{$templatemoId}");

                    if (!$extractPath) {
                        $this->templateModel->updateStatus($templateId, 'error');
                        throw new \Exception('Failed to download and extract template');
                    }

                    // Parse template structure
                    $this->parseTemplateStructure($templateId, $extractPath);

                    // Update status to inactive (ready for activation)
                    $this->templateModel->updateStatus($templateId, 'inactive');
                } catch (\Exception $e) {
                    $this->templateModel->updateStatus($templateId, 'error');
                    throw new \Exception('Download failed: ' . $e->getMessage());
                }
            } else {
                // No download URL provided, just create the template record
                $this->templateModel->updateStatus($templateId, 'inactive');
            }

            return $templateId;
        } catch (\Exception $e) {
            if (isset($templateId)) {
                $this->templateModel->updateStatus($templateId, 'error');
            }
            throw $e;
        }
    }

    /**
     * Download and extract template from URL
     */
    protected function downloadAndExtractTemplate($url, $templateSlug)
    {
        $tempFile = tempnam(sys_get_temp_dir(), 'template_');
        $extractPath = $this->themesPath . $templateSlug . '/';

        try {
            // Download template file
            $fileHandle = fopen($tempFile, 'w');
            if (!$fileHandle) {
                throw new \Exception('Failed to create temporary file');
            }

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 300); // 5 minutes
            curl_setopt($ch, CURLOPT_FILE, $fileHandle);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            fclose($fileHandle);

            if ($result === false) {
                throw new \Exception('cURL error: ' . $error);
            }

            if ($httpCode !== 200) {
                throw new \Exception("HTTP error: {$httpCode}");
            }

            // Check if file was actually downloaded
            if (!file_exists($tempFile) || filesize($tempFile) === 0) {
                throw new \Exception('Downloaded file is empty or does not exist');
            }

            // Create extraction directory
            if (!is_dir($extractPath)) {
                mkdir($extractPath, 0755, true);
            }

            // Extract based on file type
            $fileInfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($fileInfo, $tempFile);
            finfo_close($fileInfo);

            if (strpos($mimeType, 'zip') !== false) {
                $this->extractZip($tempFile, $extractPath);
            } elseif (strpos($mimeType, 'gzip') !== false) {
                $this->extractTarGz($tempFile, $extractPath);
            } else {
                throw new \Exception('Unsupported file format');
            }

            unlink($tempFile);
            return $extractPath;
        } catch (\Exception $e) {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
            throw $e;
        }
    }

    /**
     * Extract ZIP file
     */
    protected function extractZip($zipFile, $extractPath)
    {
        $zip = new \ZipArchive();
        
        if ($zip->open($zipFile) === TRUE) {
            $zip->extractTo($extractPath);
            $zip->close();
            return true;
        }
        
        throw new \Exception('Failed to extract ZIP file');
    }

    /**
     * Extract TAR.GZ file
     */
    protected function extractTarGz($tarFile, $extractPath)
    {
        $phar = new \PharData($tarFile);
        $phar->extractTo($extractPath);
        return true;
    }

    /**
     * Parse template structure and register sections/assets
     */
    public function parseTemplateStructure($templateId, $templatePath)
    {
        if (!$this->settingModel->isAutoParseSectionsEnabled()) {
            return false;
        }

        try {
            // Find main HTML file
            $htmlFiles = $this->findHtmlFiles($templatePath);
            $mainHtmlFile = $this->findMainHtmlFile($htmlFiles);

            if (!$mainHtmlFile) {
                throw new \Exception('No main HTML file found');
            }

            // Parse HTML structure
            $this->parseHtmlStructure($templateId, $mainHtmlFile);

            // Parse and register assets
            $this->parseAndRegisterAssets($templateId, $templatePath);

            return true;
        } catch (\Exception $e) {
            log_message('error', 'Template parsing failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Find HTML files in template directory
     */
    protected function findHtmlFiles($path)
    {
        $htmlFiles = [];
        $iterator = new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($path));

        foreach ($iterator as $file) {
            if ($file->isFile() && strtolower($file->getExtension()) === 'html') {
                $htmlFiles[] = $file->getPathname();
            }
        }

        return $htmlFiles;
    }

    /**
     * Find main HTML file (usually index.html or similar)
     */
    protected function findMainHtmlFile($htmlFiles)
    {
        $priorities = ['index.html', 'home.html', 'main.html'];
        
        foreach ($priorities as $priority) {
            foreach ($htmlFiles as $file) {
                if (basename($file) === $priority) {
                    return $file;
                }
            }
        }

        // Return first HTML file if no priority match
        return $htmlFiles[0] ?? null;
    }

    /**
     * Parse HTML structure and extract sections
     */
    protected function parseHtmlStructure($templateId, $htmlFile)
    {
        $html = file_get_contents($htmlFile);
        $dom = new \DOMDocument();
        
        // Suppress warnings for malformed HTML
        libxml_use_internal_errors(true);
        $dom->loadHTML($html);
        libxml_clear_errors();

        $sections = [];
        $sortOrder = 1;

        // Common section selectors
        $sectionSelectors = [
            'header' => ['header', '.header', '#header', '.navbar', '.navigation'],
            'hero' => ['.hero', '#hero', '.banner', '.main-banner', '.jumbotron'],
            'services' => ['.services', '#services', '.features', '#features'],
            'about' => ['.about', '#about', '.about-us', '#about-us'],
            'portfolio' => ['.portfolio', '#portfolio', '.gallery', '#gallery', '.work', '#work'],
            'testimonials' => ['.testimonials', '#testimonials', '.reviews', '#reviews'],
            'contact' => ['.contact', '#contact', '.contact-us', '#contact-us'],
            'footer' => ['footer', '.footer', '#footer']
        ];

        foreach ($sectionSelectors as $type => $selectors) {
            foreach ($selectors as $selector) {
                $xpath = new \DOMXPath($dom);
                
                if (strpos($selector, '#') === 0) {
                    $elements = $xpath->query("//*[@id='" . substr($selector, 1) . "']");
                } elseif (strpos($selector, '.') === 0) {
                    $elements = $xpath->query("//*[contains(@class, '" . substr($selector, 1) . "')]");
                } else {
                    $elements = $xpath->query("//" . $selector);
                }

                if ($elements->length > 0) {
                    $element = $elements->item(0);
                    $cssClasses = $element->getAttribute('class');
                    
                    $sections[] = [
                        'template_id' => $templateId,
                        'section_name' => ucfirst($type) . ' Section',
                        'section_type' => $type,
                        'html_selector' => $selector,
                        'css_classes' => $cssClasses,
                        'is_editable' => true,
                        'sort_order' => $sortOrder++,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                    break; // Found section, move to next type
                }
            }
        }

        if (!empty($sections)) {
            $this->sectionModel->insertBatch($sections);
        }
    }

    /**
     * Parse and register template assets
     */
    protected function parseAndRegisterAssets($templateId, $templatePath)
    {
        $assets = [];
        $loadOrder = 1;

        // Find CSS files
        $cssFiles = $this->findFilesByExtension($templatePath, 'css');
        foreach ($cssFiles as $cssFile) {
            $relativePath = str_replace($this->themesPath, '', $cssFile);
            $assets[] = [
                'template_id' => $templateId,
                'asset_type' => 'css',
                'file_name' => basename($cssFile),
                'file_path' => $relativePath,
                'file_size' => filesize($cssFile),
                'is_critical' => $this->isCriticalAsset(basename($cssFile)),
                'load_order' => $loadOrder++,
                'created_at' => date('Y-m-d H:i:s')
            ];
        }

        // Find JS files
        $jsFiles = $this->findFilesByExtension($templatePath, 'js');
        $loadOrder = 1; // Reset for JS files
        foreach ($jsFiles as $jsFile) {
            $relativePath = str_replace($this->themesPath, '', $jsFile);
            $assets[] = [
                'template_id' => $templateId,
                'asset_type' => 'js',
                'file_name' => basename($jsFile),
                'file_path' => $relativePath,
                'file_size' => filesize($jsFile),
                'is_critical' => $this->isCriticalAsset(basename($jsFile)),
                'load_order' => $loadOrder++,
                'created_at' => date('Y-m-d H:i:s')
            ];
        }

        if (!empty($assets)) {
            $this->assetModel->insertBatch($assets);
        }
    }

    /**
     * Find files by extension in directory
     */
    protected function findFilesByExtension($path, $extension)
    {
        $files = [];
        $iterator = new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($path));

        foreach ($iterator as $file) {
            if ($file->isFile() && strtolower($file->getExtension()) === $extension) {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    /**
     * Determine if asset is critical
     */
    protected function isCriticalAsset($fileName)
    {
        $criticalPatterns = [
            'bootstrap', 'main', 'style', 'theme', 'template', 'critical', 'core'
        ];

        $fileName = strtolower($fileName);
        
        foreach ($criticalPatterns as $pattern) {
            if (strpos($fileName, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Create backup of current template
     */
    public function createTemplateBackup($templateSlug)
    {
        $backupPath = $this->themesPath . 'backups/';
        
        if (!is_dir($backupPath)) {
            mkdir($backupPath, 0755, true);
        }

        $sourcePath = $this->themesPath . $templateSlug . '/';
        $backupFile = $backupPath . $templateSlug . '_backup_' . date('Y-m-d_H-i-s') . '.zip';

        if (!is_dir($sourcePath)) {
            return false;
        }

        $zip = new \ZipArchive();
        
        if ($zip->open($backupFile, \ZipArchive::CREATE) !== TRUE) {
            return false;
        }

        $iterator = new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($sourcePath));

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $relativePath = str_replace($sourcePath, '', $file->getPathname());
                $zip->addFile($file->getPathname(), $relativePath);
            }
        }

        $zip->close();
        return file_exists($backupFile);
    }

    /**
     * Get template engine statistics
     */
    public function getTemplateEngineStats()
    {
        return [
            'templates' => $this->templateModel->getTemplateStats(),
            'active_template' => $this->getActiveTemplate(),
            'themes_directory_size' => $this->getDirectorySize($this->themesPath),
            'backup_enabled' => $this->settingModel->isBackupEnabled(),
            'auto_parse_enabled' => $this->settingModel->isAutoParseSectionsEnabled()
        ];
    }

    /**
     * Get directory size in bytes
     */
    protected function getDirectorySize($path)
    {
        $size = 0;
        
        if (is_dir($path)) {
            $iterator = new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($path));
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $size += $file->getSize();
                }
            }
        }

        return $size;
    }
}
