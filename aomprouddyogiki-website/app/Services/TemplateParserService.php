<?php

namespace App\Services;

use App\Models\CmsTemplateModel;
use App\Models\CmsTemplateSectionModel;
use App\Models\CmsTemplateAssetModel;

class TemplateParserService
{
    protected $templateModel;
    protected $sectionModel;
    protected $assetModel;
    protected $templateEngineService;

    public function __construct()
    {
        $this->templateModel = new CmsTemplateModel();
        $this->sectionModel = new CmsTemplateSectionModel();
        $this->assetModel = new CmsTemplateAssetModel();
        $this->templateEngineService = new TemplateEngineService();
    }

    /**
     * Parse templatemo.com page to get template information
     */
    public function parseTemplateFromTemplateM($templatemoUrl)
    {
        try {
            $html = $this->fetchPageContent($templatemoUrl);
            
            if (!$html) {
                throw new \Exception('Failed to fetch template page');
            }

            $templateInfo = $this->extractTemplateInfo($html, $templatemoUrl);
            $downloadUrl = $this->extractDownloadUrl($html);

            if ($downloadUrl) {
                $templateInfo['download_url'] = $downloadUrl;
            }

            return $templateInfo;
        } catch (\Exception $e) {
            log_message('error', 'Template parsing failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Fetch page content from URL
     */
    protected function fetchPageContent($url)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $content = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($content === false || $httpCode !== 200) {
            return false;
        }

        return $content;
    }

    /**
     * Extract template information from HTML
     */
    protected function extractTemplateInfo($html, $url)
    {
        $dom = new \DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML($html);
        libxml_clear_errors();

        $xpath = new \DOMXPath($dom);
        
        // Extract template ID from URL
        preg_match('/tm-(\d+)/', $url, $matches);
        $templatemoId = $matches[1] ?? null;

        // Extract template name
        $titleElements = $xpath->query('//title');
        $title = $titleElements->length > 0 ? $titleElements->item(0)->textContent : '';
        $name = $this->cleanTemplateName($title);

        // Extract description
        $descElements = $xpath->query('//meta[@name="description"]/@content');
        $description = $descElements->length > 0 ? $descElements->item(0)->textContent : '';

        // Extract preview image
        $previewImage = $this->extractPreviewImage($xpath, $url);

        // Extract template features/tags
        $features = $this->extractTemplateFeatures($xpath);

        return [
            'name' => $name,
            'slug' => $this->generateSlug($name, $templatemoId),
            'description' => $description,
            'templatemo_id' => 'tm-' . $templatemoId,
            'source_url' => $url,
            'preview_image' => $previewImage,
            'author' => 'TemplateMo',
            'version' => '1.0.0',
            'config_data' => [
                'features' => $features,
                'responsive' => true,
                'bootstrap_version' => $this->detectBootstrapVersion($html),
                'jquery_required' => $this->detectJqueryUsage($html),
                'layout_type' => $this->detectLayoutType($html)
            ]
        ];
    }

    /**
     * Clean template name
     */
    protected function cleanTemplateName($title)
    {
        // Remove common prefixes/suffixes
        $title = preg_replace('/^TemplateMo\s*-?\s*/i', '', $title);
        $title = preg_replace('/\s*-\s*TemplateMo.*$/i', '', $title);
        $title = preg_replace('/\s*-\s*Free.*$/i', '', $title);
        $title = preg_replace('/\s*HTML.*$/i', '', $title);
        
        return trim($title);
    }

    /**
     * Generate slug from name and ID
     */
    protected function generateSlug($name, $templatemoId)
    {
        $slug = url_title($name, '-', true);
        
        if ($templatemoId) {
            $slug = 'tm-' . $templatemoId . '-' . $slug;
        }

        return $slug;
    }

    /**
     * Extract preview image URL
     */
    protected function extractPreviewImage($xpath, $baseUrl)
    {
        // Look for preview images in common locations
        $selectors = [
            '//img[contains(@class, "preview")]/@src',
            '//img[contains(@class, "template")]/@src',
            '//img[contains(@alt, "preview")]/@src',
            '//meta[@property="og:image"]/@content',
            '//img[contains(@src, "preview")]/@src'
        ];

        foreach ($selectors as $selector) {
            $elements = $xpath->query($selector);
            if ($elements->length > 0) {
                $imageUrl = $elements->item(0)->textContent;
                return $this->resolveUrl($imageUrl, $baseUrl);
            }
        }

        return null;
    }

    /**
     * Extract template features
     */
    protected function extractTemplateFeatures($xpath)
    {
        $features = [];

        // Look for feature lists
        $featureSelectors = [
            '//ul[contains(@class, "feature")]//li',
            '//div[contains(@class, "feature")]//li',
            '//ul[contains(@class, "tag")]//li',
            '//div[contains(@class, "tag")]//span'
        ];

        foreach ($featureSelectors as $selector) {
            $elements = $xpath->query($selector);
            foreach ($elements as $element) {
                $feature = trim($element->textContent);
                if (!empty($feature) && strlen($feature) < 50) {
                    $features[] = $feature;
                }
            }
        }

        return array_unique($features);
    }

    /**
     * Detect Bootstrap version
     */
    protected function detectBootstrapVersion($html)
    {
        if (preg_match('/bootstrap[\/\-]?(\d+)/', $html, $matches)) {
            return $matches[1] . '.x';
        }

        if (strpos($html, 'bootstrap') !== false) {
            return 'detected';
        }

        return 'unknown';
    }

    /**
     * Detect jQuery usage
     */
    protected function detectJqueryUsage($html)
    {
        return strpos($html, 'jquery') !== false || strpos($html, 'jQuery') !== false;
    }

    /**
     * Detect layout type
     */
    protected function detectLayoutType($html)
    {
        if (strpos($html, 'single-page') !== false || strpos($html, 'one-page') !== false) {
            return 'single-page';
        }

        if (preg_match_all('/<a[^>]+href=["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
            $internalLinks = 0;
            foreach ($matches[1] as $href) {
                if (strpos($href, '.html') !== false && strpos($href, 'http') !== 0) {
                    $internalLinks++;
                }
            }
            
            if ($internalLinks > 3) {
                return 'multi-page';
            }
        }

        return 'single-page';
    }

    /**
     * Extract download URL
     */
    protected function extractDownloadUrl($html)
    {
        $dom = new \DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML($html);
        libxml_clear_errors();

        $xpath = new \DOMXPath($dom);

        // Look for download links
        $downloadSelectors = [
            '//a[contains(@class, "download")]/@href',
            '//a[contains(text(), "Download")]/@href',
            '//a[contains(@href, "download")]/@href',
            '//a[contains(@href, ".zip")]/@href'
        ];

        foreach ($downloadSelectors as $selector) {
            $elements = $xpath->query($selector);
            if ($elements->length > 0) {
                $downloadUrl = $elements->item(0)->textContent;
                
                // Resolve relative URLs
                if (strpos($downloadUrl, 'http') !== 0) {
                    $downloadUrl = 'https://templatemo.com' . $downloadUrl;
                }
                
                return $downloadUrl;
            }
        }

        return null;
    }

    /**
     * Resolve relative URL to absolute
     */
    protected function resolveUrl($url, $baseUrl)
    {
        if (strpos($url, 'http') === 0) {
            return $url;
        }

        $parsedBase = parse_url($baseUrl);
        $baseScheme = $parsedBase['scheme'] ?? 'https';
        $baseHost = $parsedBase['host'] ?? 'templatemo.com';

        if (strpos($url, '//') === 0) {
            return $baseScheme . ':' . $url;
        }

        if (strpos($url, '/') === 0) {
            return $baseScheme . '://' . $baseHost . $url;
        }

        return $baseScheme . '://' . $baseHost . '/' . $url;
    }

    /**
     * Get popular templatemo templates
     */
    public function getPopularTemplates()
    {
        $popularTemplates = [
            'tm-562' => 'https://templatemo.com/tm-562-space-dynamic',
            'tm-561' => 'https://templatemo.com/tm-561-purple-buzz',
            'tm-560' => 'https://templatemo.com/tm-560-astro-motion',
            'tm-559' => 'https://templatemo.com/tm-559-zay-shop',
            'tm-558' => 'https://templatemo.com/tm-558-klassy-cafe',
            'tm-557' => 'https://templatemo.com/tm-557-grad-school',
            'tm-556' => 'https://templatemo.com/tm-556-catalog-zoom',
            'tm-555' => 'https://templatemo.com/tm-555-upright',
            'tm-554' => 'https://templatemo.com/tm-554-ocean-vibes',
            'tm-553' => 'https://templatemo.com/tm-553-xtra-blog'
        ];

        $templates = [];
        
        foreach ($popularTemplates as $id => $url) {
            try {
                $templateInfo = $this->parseTemplateFromTemplateM($url);
                $templateInfo['popularity_rank'] = array_search($id, array_keys($popularTemplates)) + 1;
                $templates[] = $templateInfo;
            } catch (\Exception $e) {
                log_message('error', "Failed to parse template {$id}: " . $e->getMessage());
            }
        }

        return $templates;
    }

    /**
     * Search templatemo templates
     */
    public function searchTemplateM($query, $limit = 10)
    {
        $searchUrl = "https://templatemo.com/page/1?s=" . urlencode($query);
        
        try {
            $html = $this->fetchPageContent($searchUrl);
            
            if (!$html) {
                return [];
            }

            return $this->parseSearchResults($html, $limit);
        } catch (\Exception $e) {
            log_message('error', 'Template search failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Parse search results from templatemo
     */
    protected function parseSearchResults($html, $limit)
    {
        $dom = new \DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML($html);
        libxml_clear_errors();

        $xpath = new \DOMXPath($dom);
        $results = [];

        // Look for template links in search results
        $templateLinks = $xpath->query('//a[contains(@href, "/tm-")]/@href');
        
        $count = 0;
        foreach ($templateLinks as $link) {
            if ($count >= $limit) break;
            
            $templateUrl = $this->resolveUrl($link->textContent, 'https://templatemo.com');
            
            try {
                $templateInfo = $this->parseTemplateFromTemplateM($templateUrl);
                $results[] = $templateInfo;
                $count++;
            } catch (\Exception $e) {
                log_message('error', "Failed to parse search result: " . $e->getMessage());
            }
        }

        return $results;
    }

    /**
     * Validate template structure after download
     */
    public function validateTemplateStructure($templatePath)
    {
        $validation = [
            'valid' => true,
            'errors' => [],
            'warnings' => [],
            'structure' => []
        ];

        // Check if directory exists
        if (!is_dir($templatePath)) {
            $validation['valid'] = false;
            $validation['errors'][] = 'Template directory does not exist';
            return $validation;
        }

        // Check for HTML files
        $htmlFiles = $this->templateEngineService->findHtmlFiles($templatePath);
        if (empty($htmlFiles)) {
            $validation['valid'] = false;
            $validation['errors'][] = 'No HTML files found';
        } else {
            $validation['structure']['html_files'] = count($htmlFiles);
        }

        // Check for CSS files
        $cssFiles = $this->templateEngineService->findFilesByExtension($templatePath, 'css');
        if (empty($cssFiles)) {
            $validation['warnings'][] = 'No CSS files found';
        } else {
            $validation['structure']['css_files'] = count($cssFiles);
        }

        // Check for JS files
        $jsFiles = $this->templateEngineService->findFilesByExtension($templatePath, 'js');
        $validation['structure']['js_files'] = count($jsFiles);

        // Check for images
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'];
        $imageCount = 0;
        foreach ($imageExtensions as $ext) {
            $imageCount += count($this->templateEngineService->findFilesByExtension($templatePath, $ext));
        }
        $validation['structure']['image_files'] = $imageCount;

        // Check directory size
        $dirSize = $this->templateEngineService->getDirectorySize($templatePath);
        $validation['structure']['total_size'] = $dirSize;
        
        if ($dirSize > 100 * 1024 * 1024) { // 100MB
            $validation['warnings'][] = 'Template size is very large (>100MB)';
        }

        return $validation;
    }
}
